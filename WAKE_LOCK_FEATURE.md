# 防休眠功能 (Wake Lock Feature)

## 功能概述

我们为在线时钟应用添加了防休眠功能，当用户进入全屏模式时，系统会自动防止电脑进入休眠状态或激活屏幕保护程序。

## 功能特点

### 🔒 自动防休眠
- 进入全屏模式时自动激活防休眠
- 退出全屏模式时自动释放防休眠
- 支持页面可见性变化时的智能重新激活

### 🌐 浏览器兼容性
- 使用现代 Screen Wake Lock API
- 支持 Chrome 84+、Edge 84+、Opera 70+ 等现代浏览器
- 对不支持的浏览器提供友好的提示信息

### 📱 跨平台支持
- 桌面端：Windows、macOS、Linux
- 移动端：Android（部分支持）
- 注意：iOS Safari 目前不支持此功能

## 技术实现

### 自定义 Hooks

#### `useWakeLock`
```typescript
const { wakeLock, isSupported, isActive, requestWakeLock, releaseWakeLock } = useWakeLock();
```

#### `useFullscreenWakeLock`
```typescript
const { isFullScreen, isWakeLockSupported, isWakeLockActive } = useFullscreenWakeLock();
```

### 组件集成
- 所有页面（时钟、闹钟、计时器、秒表）都支持防休眠功能
- 统一的状态指示器显示防休眠状态
- 自动清理和错误处理

## 用户体验

### 状态指示器
- 🔒 绿色：防休眠已激活
- ⏸️ 灰色：防休眠未激活
- ⚠️ 黄色：浏览器不支持防休眠功能

### 使用场景
- 📊 演示和展示
- 🧘 冥想和专注时间
- ⏰ 长时间计时需求
- 🎯 需要持续显示时间的场景

## 安全性和隐私

- 仅在全屏模式下激活
- 用户主动触发，无后台自动激活
- 退出全屏或关闭页面时自动释放
- 不收集任何用户数据

## 浏览器支持状态

| 浏览器 | 桌面端 | 移动端 | 版本要求 |
|--------|--------|--------|----------|
| Chrome | ✅ | ✅ | 84+ |
| Edge | ✅ | ✅ | 84+ |
| Firefox | ❌ | ❌ | 计划中 |
| Safari | ❌ | ❌ | 不支持 |
| Opera | ✅ | ✅ | 70+ |

## 故障排除

### 常见问题

1. **防休眠不工作**
   - 检查浏览器是否支持 Wake Lock API
   - 确保已进入全屏模式
   - 检查浏览器权限设置

2. **页面切换后失效**
   - 系统会自动检测页面可见性变化
   - 重新进入页面时会自动重新激活

3. **移动设备上的限制**
   - 某些移动浏览器可能有额外限制
   - 建议在桌面端使用以获得最佳体验

## 开发说明

### 添加到新页面
```typescript
import { useFullscreenWakeLock } from "@/hooks/useFullscreenWakeLock";
import { WakeLockIndicator } from "@/components/ui/wake-lock-indicator";

function MyComponent() {
  const { isFullScreen } = useFullscreenWakeLock();
  
  return (
    <>
      {/* 你的组件内容 */}
      <WakeLockIndicator isFullScreen={isFullScreen} />
    </>
  );
}
```

### 自定义配置
可以通过修改 `useWakeLock` hook 来调整防休眠行为，例如添加用户偏好设置或条件激活。 