# Best Online Clock

A modern online clock application built with Next.js, inspired by vclock.com but with a cooler UI and enhanced features.

## Features

### Time
- Digital clock display with customizable appearance
- Options to show/hide seconds, weekday, date, and week number
- Customizable text color, font size, and font family
- Settings are saved in local storage

### Alarm
- Set alarms with custom time, title, and sound
- Enable/disable alarms
- Snooze functionality (1, 5, or 10 minutes)
- Alarm notification with sound

### Timer
- Countdown timer with visual progress indicator
- Preset timer durations
- Custom timer settings (hours, minutes, seconds)
- Notification when timer completes

### Stopwatch
- Start, pause, and reset functionality
- Lap time recording
- Display of lap times and total elapsed time

## Mobile Responsiveness
- Fully responsive design for all screen sizes
- Optimized for mobile devices

## Full-Screen Mode
- Toggle full-screen mode for distraction-free viewing
- Navigation bar hides in full-screen mode (appears on hover)
- Core functionality remains visible in full-screen mode

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Run the development server:
   ```
   npm run dev
   ```
4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Adding Sound Files

To add alarm sounds, place MP3 files in the `public/sounds` directory with the following names:
- default.mp3
- bell.mp3
- digital.mp3
- gentle.mp3

Also add a `timer-complete.mp3` file for the timer completion sound.

## Technologies Used

- Next.js
- React
- TypeScript
- Tailwind CSS
- Radix UI components

## License

MIT
