import { useState, useEffect, useCallback } from 'react';

export function useWakeLock() {
  const [wakeLock, setWakeLock] = useState<WakeLockSentinel | null>(null);
  const [isSupported, setIsSupported] = useState(false);

  // Check if Wake Lock API is supported
  useEffect(() => {
    const supported = 'wakeLock' in navigator;
    setIsSupported(supported);
    console.log('Wake Lock API support check:', {
      supported,
      userAgent: navigator.userAgent,
      wakeLockAvailable: 'wakeLock' in navigator,
      navigatorKeys: Object.keys(navigator)
    });
  }, []);

  const requestWakeLock = useCallback(async () => {
    try {
      if (!isSupported) {
        console.warn('Wake Lock API is not supported in this browser');
        return false;
      }

      if (wakeLock) {
        console.log('Wake lock already active');
        return true;
      }

      const wakeLockSentinel = await navigator.wakeLock.request('screen');
      setWakeLock(wakeLockSentinel);
      console.log('Screen wake lock activated');
      
      wakeLockSentinel.addEventListener('release', () => {
        console.log('Screen wake lock released');
        setWakeLock(null);
      });

      return true;
    } catch (err) {
      console.error('Failed to request wake lock:', err);
      return false;
    }
  }, [isSupported, wakeLock]);

  const releaseWakeLock = useCallback(async () => {
    if (wakeLock) {
      try {
        await wakeLock.release();
        setWakeLock(null);
        console.log('Screen wake lock manually released');
        return true;
      } catch (err) {
        console.error('Failed to release wake lock:', err);
        return false;
      }
    }
    return true;
  }, [wakeLock]);

  // Clean up wake lock on unmount
  useEffect(() => {
    return () => {
      if (wakeLock) {
        wakeLock.release().catch(console.error);
      }
    };
  }, [wakeLock]);

  return {
    wakeLock,
    isSupported,
    isActive: !!wakeLock,
    requestWakeLock,
    releaseWakeLock,
  };
} 