import { useState, useEffect } from 'react';
import { useWakeLock } from './useWakeLock';
import { isFullscreenActive, isSimulatedFullscreenActive, addFullscreenChangeListener } from '@/lib/fullscreen';

export function useFullscreenWakeLock() {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isSimulatedFullScreen, setIsSimulatedFullScreen] = useState(false);
  const { requestWakeLock, releaseWakeLock, isSupported, isActive } = useWakeLock();

  // Monitor fullscreen state and manage wake lock
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullScreenNow = isFullscreenActive();
      const isSimulatedNow = isSimulatedFullscreenActive();

      setIsFullScreen(isFullScreenNow);
      setIsSimulatedFullScreen(isSimulatedNow);

      // Manage wake lock based on fullscreen state
      if (isFullScreenNow) {
        requestWakeLock();
      } else {
        releaseWakeLock();
      }

      // Hide/show SEO content based on fullscreen state
      const seoSection = document.querySelector('[data-seo-content]');
      if (seoSection) {
        if (isFullScreenNow) {
          (seoSection as HTMLElement).style.display = 'none';
        } else {
          (seoSection as HTMLElement).style.display = 'block';
        }
      }

      // Disable/enable scrolling in fullscreen mode
      const htmlElement = document.documentElement;
      const bodyElement = document.body;

      if (isFullScreenNow) {
        // Disable scrolling
        htmlElement.style.overflow = 'hidden';
        bodyElement.style.overflow = 'hidden';
      } else {
        // Re-enable scrolling
        htmlElement.style.overflow = '';
        bodyElement.style.overflow = '';
      }
    };

    // Use the utility function to add cross-browser event listeners
    const cleanup = addFullscreenChangeListener(handleFullscreenChange);

    return cleanup;
  }, [requestWakeLock, releaseWakeLock]);

  // Handle page visibility changes to re-request wake lock if needed
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isFullScreen && !isActive) {
        requestWakeLock();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isFullScreen, isActive, requestWakeLock]);

  return {
    isFullScreen,
    isSimulatedFullScreen,
    isWakeLockSupported: isSupported,
    isWakeLockActive: isActive,
  };
}