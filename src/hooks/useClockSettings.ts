"use client";

import { useState, useEffect, useCallback } from "react";

export interface ClockSettings {
  showSeconds: boolean;
  showWeekday: boolean;
  showDate: boolean;
  showWeekNumber: boolean;
  use12Hours: boolean;
  textColor: string;
  fontSize: string;
  fontFamily: string;
  position: { x: number; y: number };
  backgroundColor: string;
  backgroundImage: string;
}

const defaultSettings: ClockSettings = {
  showSeconds: true,
  showWeekday: true,
  showDate: true,
  showWeekNumber: true,
  use12Hours: false,
  textColor: "#000000",
  fontSize: "6rem",
  fontFamily: "monospace",
  position: { x: 0, y: 0 },
  backgroundColor: "",
  backgroundImage: "",
};

const STORAGE_KEY = "clockSettings";

export function useClockSettings() {
  const [settings, setSettings] = useState<ClockSettings>(defaultSettings);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    let mounted = true;

    const loadSettings = async () => {
      if (typeof window !== 'undefined') {
        try {
          const savedSettings = localStorage.getItem(STORAGE_KEY);
          console.log("Loading settings from localStorage:", savedSettings);

          if (savedSettings && mounted) {
            const parsed = JSON.parse(savedSettings);
            console.log("Parsed settings:", parsed);

            // Merge with defaults to ensure all properties exist
            const mergedSettings = { ...defaultSettings, ...parsed };
            setSettings(mergedSettings);
          }
        } catch (error) {
          console.error("Error loading settings from localStorage:", error);
        } finally {
          if (mounted) {
            setIsLoaded(true);
          }
        }
      }
    };

    loadSettings();

    return () => {
      mounted = false;
    };
  }, []);

  // Save settings to localStorage when they change (with debounce)
  useEffect(() => {
    if (!isLoaded) return;

    const timeoutId = setTimeout(() => {
      if (typeof window !== 'undefined') {
        try {
          const settingsString = JSON.stringify(settings);
          localStorage.setItem(STORAGE_KEY, settingsString);
          console.log("Settings saved to localStorage:", settingsString);
        } catch (error) {
          console.error("Error saving settings to localStorage:", error);
        }
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [settings, isLoaded]);

  // Individual setters with memoization to prevent unnecessary updates
  const updateSetting = useCallback(<K extends keyof ClockSettings>(
    key: K,
    value: ClockSettings[K]
  ) => {
    setSettings(prev => {
      // Only update if the value actually changed
      if (prev[key] === value) {
        return prev;
      }
      return { ...prev, [key]: value };
    });
  }, []);

  return {
    settings,
    isLoaded,
    updateSetting,
    // Individual getters for convenience
    showSeconds: settings.showSeconds,
    showWeekday: settings.showWeekday,
    showDate: settings.showDate,
    showWeekNumber: settings.showWeekNumber,
    use12Hours: settings.use12Hours,
    textColor: settings.textColor,
    fontSize: settings.fontSize,
    fontFamily: settings.fontFamily,
    position: settings.position,
    backgroundColor: settings.backgroundColor,
    backgroundImage: settings.backgroundImage,
    // Individual setters for convenience (memoized)
    setShowSeconds: useCallback((value: boolean) => updateSetting('showSeconds', value), [updateSetting]),
    setShowWeekday: useCallback((value: boolean) => updateSetting('showWeekday', value), [updateSetting]),
    setShowDate: useCallback((value: boolean) => updateSetting('showDate', value), [updateSetting]),
    setShowWeekNumber: useCallback((value: boolean) => updateSetting('showWeekNumber', value), [updateSetting]),
    setUse12Hours: useCallback((value: boolean) => updateSetting('use12Hours', value), [updateSetting]),
    setTextColor: useCallback((value: string) => updateSetting('textColor', value), [updateSetting]),
    setFontSize: useCallback((value: string) => updateSetting('fontSize', value), [updateSetting]),
    setFontFamily: useCallback((value: string) => updateSetting('fontFamily', value), [updateSetting]),
    setPosition: useCallback((value: { x: number; y: number }) => updateSetting('position', value), [updateSetting]),
    setBackgroundColor: useCallback((value: string) => updateSetting('backgroundColor', value), [updateSetting]),
    setBackgroundImage: useCallback((value: string) => updateSetting('backgroundImage', value), [updateSetting]),
  };
}
