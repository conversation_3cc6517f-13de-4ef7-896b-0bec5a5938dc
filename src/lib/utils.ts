import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatTime(date: Date, showSeconds: boolean = true, use12Hours: boolean = false): string {
  let hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");

  let ampm = "";
  if (use12Hours) {
    ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
  }

  const formattedHours = (use12Hours ? hours : hours.toString().padStart(2, "0"));

  return showSeconds
    ? `${formattedHours}:${minutes}:${seconds}${use12Hours ? ` ${ampm}` : ""}`
    : `${formattedHours}:${minutes}${use12Hours ? ` ${ampm}` : ""}`;
}

export function formatDate(date: Date): string {
  return date.toLocaleDateString(undefined, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

export function getWeekday(date: Date): string {
  return date.toLocaleDateString(undefined, { weekday: "long" });
}

export function getShortWeekday(date: Date): string {
  return date.toLocaleDateString(undefined, { weekday: "short" });
}

export function getShortMonth(date: Date): string {
  return date.toLocaleDateString(undefined, { month: "short" });
}

export function getWeekNumber(date: Date): number {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
  const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

export function formatDateWithWeek(date: Date): string {
  const weekday = getShortWeekday(date);
  const month = getShortMonth(date);
  const day = date.getDate();
  const year = date.getFullYear();
  const weekNumber = getWeekNumber(date);

  return `${weekday} - ${month} ${day}, ${year} - Week ${weekNumber}`;
}

export function formatTimeForInput(date: Date): string {
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
}

export function formatTimeWithMilliseconds(time: number, format: string = "00:00.00"): string {
  const hours = Math.floor(time / 3600000);
  const minutes = Math.floor((time % 3600000) / 60000);
  const seconds = Math.floor((time % 60000) / 1000);
  const milliseconds = time % 1000;

  switch (format) {
    case "00:00.000":
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${milliseconds.toString().padStart(3, "0")}`;
      }
      return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${milliseconds.toString().padStart(3, "0")}`;

    case "00:00.00":
      const centiseconds = Math.floor(milliseconds / 10);
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${centiseconds.toString().padStart(2, "0")}`;
      }
      return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${centiseconds.toString().padStart(2, "0")}`;

    case "00:00.0":
      const deciseconds = Math.floor(milliseconds / 100);
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${deciseconds}`;
      }
      return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${deciseconds}`;

    case "00:00":
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
      }
      return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

    default:
      // Default to 00:00.00 format
      const defaultCentiseconds = Math.floor(milliseconds / 10);
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${defaultCentiseconds.toString().padStart(2, "0")}`;
      }
      return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${defaultCentiseconds.toString().padStart(2, "0")}`;
  }
}

export function formatCountdownTime(time: number): string {
  const hours = Math.floor(time / 3600000);
  const minutes = Math.floor((time % 3600000) / 60000);
  const seconds = Math.floor((time % 60000) / 1000);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }

  return `${minutes}:${seconds.toString().padStart(2, "0")}`;
}

export async function toggleFullScreen(): Promise<void> {
  try {
    const isCurrentlyFullScreen = !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );

    if (!isCurrentlyFullScreen) {
      // Try to enter fullscreen with different browser APIs
      const docElement = document.documentElement as any;

      if (docElement.requestFullscreen) {
        await docElement.requestFullscreen();
      } else if (docElement.webkitRequestFullscreen) {
        await docElement.webkitRequestFullscreen();
      } else if (docElement.mozRequestFullScreen) {
        await docElement.mozRequestFullScreen();
      } else if (docElement.msRequestFullscreen) {
        await docElement.msRequestFullscreen();
      } else {
        console.warn('Fullscreen API not supported on this device');
        return;
      }
    } else {
      // Try to exit fullscreen with different browser APIs
      const doc = document as any;

      if (doc.exitFullscreen) {
        await doc.exitFullscreen();
      } else if (doc.webkitExitFullscreen) {
        await doc.webkitExitFullscreen();
      } else if (doc.mozCancelFullScreen) {
        await doc.mozCancelFullScreen();
      } else if (doc.msExitFullscreen) {
        await doc.msExitFullscreen();
      }
    }
  } catch (err) {
    console.error(`Error toggling full-screen mode: ${err}`);
  }
}
