import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Privacy Policy - Best Online Clock",
  description: "Best online clock privacy policy. We do not collect any personal information from our users.",  
  alternates: {
    canonical: '/privacy-policy',
  },
};

export default function PrivacyPolicyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
