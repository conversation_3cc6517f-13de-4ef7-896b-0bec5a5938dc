import { MainLayout } from "@/components/layout/main-layout";
import { StopwatchSEOContent } from "@/components/seo/stopwatch-seo-content";
import { StructuredData } from "@/components/seo/structured-data";
import { StopwatchClient } from "./stopwatch-client";

export default function StopwatchPage() {
  return (
    <MainLayout>
      <StructuredData type="stopwatch" />

      {/* Full-height stopwatch section */}
      <section className="relative flex flex-col" style={{ height: 'calc(100vh - 4rem)', overflow: 'hidden' }}>
        <StopwatchClient />
      </section>

      {/* SEO content section - below the fold, always rendered for SEO */}
      <section className="relative z-9" data-seo-content>
        <StopwatchSEOContent />
      </section>
    </MainLayout>
  );
}
