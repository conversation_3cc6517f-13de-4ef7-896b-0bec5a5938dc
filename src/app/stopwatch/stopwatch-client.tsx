"use client";

import { useState, useEffect } from "react";
import { StopwatchDisplay } from "@/components/stopwatch/stopwatch-display";
import { StopwatchSettings } from "@/components/stopwatch/stopwatch-settings";
import { useFullscreenWakeLock } from "@/hooks/useFullscreenWakeLock";
import { WakeLockIndicator } from "@/components/ui/wake-lock-indicator";

// Simple localStorage utilities
const STORAGE_KEY = "stopwatchSettings";
const DEFAULT_TEXT_COLOR = "#80ff80";
const DEFAULT_BACKGROUND_IMAGE = "linear-gradient(135deg, #2c5364, #203a43, #0f2027)";

const defaultSettings = {
  timeFormat: "00:00.00",
  textColor: DEFAULT_TEXT_COLOR,
  fontSize: "4rem",
  fontFamily: "JetBrains Mono, Menlo, Consolas, monospace",
  position: { x: 0, y: 0 },
  backgroundColor: "",
  backgroundImage: "",
};

export function StopwatchClient() {
  const [settings, setSettings] = useState(defaultSettings);
  const [isLoaded, setIsLoaded] = useState(false);

  // Use the custom hook for fullscreen and wake lock management
  const { isFullScreen, isWakeLockSupported, isWakeLockActive } = useFullscreenWakeLock();

  // Load settings on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setSettings({ ...defaultSettings, ...parsed });
      }
    } catch (error) {
      console.error("Error loading settings:", error);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save settings when they change
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
      } catch (error) {
        console.error("Error saving settings:", error);
      }
    }
  }, [settings, isLoaded]);

  // Extract settings for easier access
  const {
    timeFormat,
    textColor,
    fontSize,
    fontFamily,
    position,
    backgroundColor,
    backgroundImage,
  } = settings;

  // Update setting function
  const updateSetting = (key: string, value: string | { x: number; y: number }) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // Individual setter functions
  const setTimeFormat = (value: string) => updateSetting('timeFormat', value);
  const setTextColor = (value: string) => updateSetting('textColor', value);
  const setFontSize = (value: string) => updateSetting('fontSize', value);
  const setFontFamily = (value: string) => updateSetting('fontFamily', value);
  const setPosition = (value: { x: number; y: number }) => updateSetting('position', value);
  const setBackgroundColor = (value: string) => updateSetting('backgroundColor', value);
  const setBackgroundImage = (value: string) => updateSetting('backgroundImage', value);

  // Apply background styles to the entire page (only to html element to avoid duplication)
  useEffect(() => {
    if (!isLoaded || typeof document === 'undefined') return;

    const htmlElement = document.documentElement;

    // Apply background styles only to html element
    if (backgroundImage) {
      htmlElement.style.backgroundImage = `url("${backgroundImage}")`;
      htmlElement.style.backgroundColor = '';
    } else if (backgroundColor) {
      htmlElement.style.backgroundColor = backgroundColor;
      htmlElement.style.backgroundImage = '';
    } else {
      htmlElement.style.backgroundColor = '';
      htmlElement.style.backgroundImage = DEFAULT_BACKGROUND_IMAGE;
    }

    // Set common background properties
    htmlElement.style.backgroundSize = 'cover';
    htmlElement.style.backgroundPosition = 'center';
    htmlElement.style.backgroundRepeat = 'no-repeat';
    htmlElement.style.backgroundAttachment = 'fixed';

    // No cleanup on unmount to avoid interfering with other pages' backgrounds
  }, [backgroundColor, backgroundImage, isLoaded]);

  // Note: We render the settings panel immediately for SEO, even if not loaded
  // The settings panel will use default values until localStorage is loaded

  return (
    <>
      {isFullScreen ? (
        // Full screen layout - settings button in bottom-right corner
        <>
          <div className="flex items-center justify-center min-h-screen">
            <StopwatchDisplay
              timeFormat={timeFormat}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Settings panel - floating in bottom-right corner for fullscreen */}
          <div className="fixed bottom-4 right-4 z-50 md:top-4 md:bottom-auto">
            <StopwatchSettings
              timeFormat={timeFormat}
              setTimeFormat={setTimeFormat}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
            />
          </div>

          {/* Wake Lock Status Indicator */}
          <WakeLockIndicator 
            isFullScreen={isFullScreen} 
            isSupported={isWakeLockSupported}
            isActive={isWakeLockActive}
          />
        </>
      ) : (
        // Non-fullscreen layout - better mobile positioning
        <>
          <div
            className="w-full flex items-center justify-center px-4 py-16 md:py-6"
            style={{
              minHeight: 'calc(100vh - 120px)', // Account for navigation
            }}
          >
            <StopwatchDisplay
              timeFormat={timeFormat}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Settings panel - mobile: bottom-right, desktop: top-right */}
          <div className="fixed bottom-4 right-4 z-50 md:top-20 md:bottom-auto md:right-4">
            <StopwatchSettings
              timeFormat={timeFormat}
              setTimeFormat={setTimeFormat}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
            />
          </div>
        </>
      )}
    </>
  );
}
