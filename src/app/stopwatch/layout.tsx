import type { Metadata } from "next";

/*
export const metadata: Metadata = {
  title: "Online Stopwatch - Precision Timing, Laps & Data Export",
  description: "Best precision stopwatch online! Record lap times, export data & multiple formats. Fullscreen mode keeps your screen awake—never lose track of your timing.",
  openGraph: {
    title: "Online Stopwatch - Precision Timing, Laps & Data Export",
    description: "Best precision stopwatch online! Record lap times, export data & multiple formats. Fullscreen mode keeps your screen awake—never lose track of your timing.",
    url: 'https://bestonlineclock.com/stopwatch',
    images: [
      {
        url: '/stopwatch-og-image.png',
        width: 1200,
        height: 630,
        alt: 'Online Stopwatch Interface',
      },
    ],
  },
  twitter: {
    title: "Online Stopwatch - Precision Timing, Laps & Data Export",
    description: "Best precision stopwatch online! Record lap times, export data & multiple formats. Fullscreen mode keeps your screen awake—never lose track of your timing.",
    images: ['/stopwatch-twitter-image.png'],
  },
  alternates: {
    canonical: '/stopwatch',
  },
};
*/
export const metadata: Metadata = {
  title: "Online Stopwatch - Precision Timing, Laps & Data Export",
  description: "Best precision stopwatch online! Record lap times, export data & multiple formats. Fullscreen mode keeps your screen awake—never lose track of your timing.",
  alternates: {
    canonical: '/stopwatch',
  },
};

export default function StopwatchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
