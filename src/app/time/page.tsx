import { MainLayout } from "@/components/layout/main-layout";
import { WorldTimeSEOContent } from "@/components/seo/world-time-seo-content";
import { StructuredData } from "@/components/seo/structured-data";
import { WorldTimeClient } from "./world-time-client";
import { Metadata } from "next";

// Generate metadata for world time page
export const metadata: Metadata = {
  title: 'World Time Zones - Current Time in Major Cities | Best Online Clock',
  description: 'Check current time in major cities around the world. Real-time world clock with timezone information for New York, London, Tokyo, Beijing, and more. Perfect for international business and travel planning.',
  keywords: [
    'world time',
    'world clock',
    'international time',
    'timezone converter',
    'current time worldwide',
    'global time zones',
    'city time',
    'world time zones',
    'international clock',
    'time around the world'
  ],
  openGraph: {
    title: 'World Time Zones - Current Time in Major Cities',
    description: 'Check current time in major cities around the world. Real-time world clock with timezone information.',
    type: 'website',
    url: 'https://bestonlineclock.com/time',
  },
  twitter: {
    card: 'summary',
    title: 'World Time Zones - Current Time in Major Cities',
    description: 'Check current time in major cities around the world. Real-time world clock with timezone information.',
  },
  alternates: {
    canonical: 'https://bestonlineclock.com/time',
  },
};

export default function WorldTimePage() {
  return (
    <MainLayout>
      <StructuredData type="homepage" />

      {/* Full-height clock section */}
      <section className="relative" style={{ height: 'calc(100vh - 4rem)' }}>
        <WorldTimeClient />
      </section>

      {/* SEO content section - below the fold */}
      <section className="relative z-9" data-seo-content>
        <WorldTimeSEOContent />
      </section>
    </MainLayout>
  );
}
