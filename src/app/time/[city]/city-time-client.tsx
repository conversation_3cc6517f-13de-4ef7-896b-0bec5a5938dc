"use client";

import { useState, useEffect } from "react";
import { TimezoneClock } from "@/components/time/timezone-clock";
import { ClockSettings } from "@/components/time/clock-settings";
import { useFullscreenWakeLock } from "@/hooks/useFullscreenWakeLock";
import { WakeLockIndicator } from "@/components/ui/wake-lock-indicator";
import { City } from "@/lib/timezone";

// Simple localStorage utilities
const STORAGE_KEY = "cityTimeSettings";
const DEFAULT_TEXT_COLOR = "#00ff88";
const DEFAULT_BACKGROUND_IMAGE = "linear-gradient(135deg, #48c6ef 0%, #6f35c5 30%, #1a2980 60%, #2f3f87 100%)";

const defaultSettings = {
  showSeconds: true,
  showWeekday: true,
  showDate: true,
  showWeekNumber: true,
  use12Hours: false,
  textColor: DEFAULT_TEXT_COLOR,
  fontSize: "5rem",
  fontFamily: "monospace",
  position: { x: 0, y: 0 },
  backgroundColor: "",
  backgroundImage: DEFAULT_BACKGROUND_IMAGE,
};

interface CityTimeClientProps {
  city: City;
}

export function CityTimeClient({ city }: CityTimeClientProps) {
  const [settings, setSettings] = useState(defaultSettings);
  const [isLoaded, setIsLoaded] = useState(false);
  
  // Use the custom hook for fullscreen and wake lock management
  const { isFullScreen, isWakeLockSupported, isWakeLockActive } = useFullscreenWakeLock();

  // Destructure settings for easier access
  const {
    showSeconds,
    showWeekday,
    showDate,
    showWeekNumber,
    use12Hours,
    textColor,
    fontSize,
    fontFamily,
    position,
    backgroundColor,
    backgroundImage
  } = settings;

  // Load settings from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsedSettings = JSON.parse(saved);
        setSettings({ ...defaultSettings, ...parsedSettings });
      }
    } catch (error) {
      console.error("Failed to load settings:", error);
    }
    setIsLoaded(true);
  }, []);

  // Save settings to localStorage whenever they change
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
      } catch (error) {
        console.error("Failed to save settings:", error);
      }
    }
  }, [settings, isLoaded]);

  // Individual setter functions
  const setShowSeconds = (value: boolean) => setSettings(prev => ({ ...prev, showSeconds: value }));
  const setShowWeekday = (value: boolean) => setSettings(prev => ({ ...prev, showWeekday: value }));
  const setShowDate = (value: boolean) => setSettings(prev => ({ ...prev, showDate: value }));
  const setShowWeekNumber = (value: boolean) => setSettings(prev => ({ ...prev, showWeekNumber: value }));
  const setUse12Hours = (value: boolean) => setSettings(prev => ({ ...prev, use12Hours: value }));
  const setTextColor = (value: string) => setSettings(prev => ({ ...prev, textColor: value }));
  const setFontSize = (value: string) => setSettings(prev => ({ ...prev, fontSize: value }));
  const setFontFamily = (value: string) => setSettings(prev => ({ ...prev, fontFamily: value }));
  const setPosition = (value: { x: number; y: number }) => setSettings(prev => ({ ...prev, position: value }));
  const setBackgroundColor = (value: string) => setSettings(prev => ({ ...prev, backgroundColor: value }));
  const setBackgroundImage = (value: string) => setSettings(prev => ({ ...prev, backgroundImage: value }));

  // Apply background styles to html element
  useEffect(() => {
    if (!isLoaded || typeof document === 'undefined') return;

    const htmlElement = document.documentElement;

    if (backgroundImage) {
      // Check if it's a URL or base64 data
      if (backgroundImage.startsWith('data:') || backgroundImage.startsWith('http')) {
        htmlElement.style.backgroundImage = `url("${backgroundImage}")`;
      } else {
        // It's a CSS gradient
        htmlElement.style.backgroundImage = backgroundImage;
      }
      htmlElement.style.backgroundColor = '';
    } else if (backgroundColor) {
      htmlElement.style.backgroundColor = backgroundColor;
      htmlElement.style.backgroundImage = '';
    } else {
      htmlElement.style.backgroundColor = '';
      htmlElement.style.backgroundImage = DEFAULT_BACKGROUND_IMAGE;
    }

    htmlElement.style.backgroundSize = 'cover';
    htmlElement.style.backgroundPosition = 'center';
    htmlElement.style.backgroundRepeat = 'no-repeat';
    htmlElement.style.backgroundAttachment = 'fixed';
  }, [backgroundColor, backgroundImage, isLoaded]);

  return (
    <>
      {isFullScreen ? (
        <>
          <div
            className="w-full h-screen flex items-center justify-center"
            style={{
              minHeight: '100vh', // Full viewport height
            }}
          >
            <TimezoneClock
              city={city}
              showSeconds={showSeconds}
              showWeekday={showWeekday}
              showDate={showDate}
              showWeekNumber={showWeekNumber}
              use12Hours={use12Hours}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Fullscreen settings panel - top-right */}
          <div className="fixed top-4 right-4 z-50">
            <ClockSettings
              showSeconds={showSeconds}
              setShowSeconds={setShowSeconds}
              showWeekday={showWeekday}
              setShowWeekday={setShowWeekday}
              showDate={showDate}
              setShowDate={setShowDate}
              showWeekNumber={showWeekNumber}
              setShowWeekNumber={setShowWeekNumber}
              use12Hours={use12Hours}
              setUse12Hours={setUse12Hours}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
            />
          </div>

          {/* Wake Lock Status Indicator */}
          <WakeLockIndicator 
            isFullScreen={isFullScreen} 
            isSupported={isWakeLockSupported}
            isActive={isWakeLockActive}
          />
        </>
      ) : (
        <>
          <div
            className="absolute inset-0 flex items-center justify-center"
          >
            <TimezoneClock
              city={city}
              showSeconds={showSeconds}
              showWeekday={showWeekday}
              showDate={showDate}
              showWeekNumber={showWeekNumber}
              use12Hours={use12Hours}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Scroll indicator - centered at bottom */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
            <div className="flex flex-col items-center space-y-2 text-white/70 animate-bounce">
              <span className="text-sm font-medium">Scroll for more info</span>
              <svg 
                className="w-6 h-6" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M19 14l-7 7m0 0l-7-7m7 7V3" 
                />
              </svg>
            </div>
          </div>

          {/* Settings panel - mobile: bottom-right, desktop: top-right */}
          <div className="fixed bottom-4 right-4 md:top-20 md:bottom-auto md:right-4 z-50">
            <ClockSettings
              showSeconds={showSeconds}
              setShowSeconds={setShowSeconds}
              showWeekday={showWeekday}
              setShowWeekday={setShowWeekday}
              showDate={showDate}
              setShowDate={setShowDate}
              showWeekNumber={showWeekNumber}
              setShowWeekNumber={setShowWeekNumber}
              use12Hours={use12Hours}
              setUse12Hours={setUse12Hours}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
            />
          </div>

          {/* Wake Lock Status Indicator */}
          <WakeLockIndicator 
            isFullScreen={isFullScreen} 
            isSupported={isWakeLockSupported}
            isActive={isWakeLockActive}
          />
        </>
      )}
    </>
  );
}
