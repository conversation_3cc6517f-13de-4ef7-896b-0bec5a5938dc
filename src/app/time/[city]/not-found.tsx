import Link from 'next/link';
import { MainLayout } from "@/components/layout/main-layout";
import { getAllCities } from "@/lib/timezone";

export default function CityNotFound() {
  const cities = getAllCities();

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-4xl mx-auto pt-32 px-4 pb-8">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              City Not Found
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Sorry, we don't have time information for this city yet. 
              Please choose from one of the available cities below.
            </p>
          </div>

          {/* Available Cities */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-center mb-8 text-gray-900 dark:text-white">
              Available World Time Zones
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {cities.map((city) => (
                <Link
                  key={city.id}
                  href={`/time/${city.id}`}
                  className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600"
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {city.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">
                    {city.country}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">
                    {city.timezone}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    UTC{city.utcOffset >= 0 ? '+' : ''}{city.utcOffset}
                  </p>
                </Link>
              ))}
            </div>
          </div>

          {/* Navigation Links */}
          <div className="text-center">
            <h3 className="text-xl font-semibold mb-6 text-gray-900 dark:text-white">
              Or explore our other tools
            </h3>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                href="/"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Main Clock
              </Link>
              <Link
                href="/timer"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Timer
              </Link>
              <Link
                href="/alarm"
                className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Alarm
              </Link>
              <Link
                href="/stopwatch"
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Stopwatch
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
