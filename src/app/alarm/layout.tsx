import type { Metadata } from "next";
/*
export const metadata: Metadata = {
  title: "Online Alarm Clock - No Sleep in Fullscreen, Multi Alarms",
  description: "Best alarm clock online! Set multiple wake-up alarms with custom sounds. Fullscreen mode keeps your screen awake—never miss an alarm or reminder.",
  openGraph: {
    title: "Online Alarm Clock - Multiple Alarms & Custom Sounds (No Sleep in Fullscreen)",
    description: "Best alarm clock online! Set multiple wake-up alarms with custom sounds. Fullscreen mode keeps your screen awake—never miss an alarm or reminder.",
    url: 'https://bestonlineclock.com/alarm',
    images: [
      {
        url: '/alarm-og-image.png',
        width: 1200,
        height: 630,
        alt: 'Online Alarm Clock Interface',
      },
    ],
  },
  twitter: {
    title: "Online Alarm Clock - Multiple Alarms & Custom Sounds (No Sleep in Fullscreen)",
    description: "Best alarm clock online! Set multiple wake-up alarms with custom sounds. Fullscreen mode keeps your screen awake—never miss an alarm or reminder.",
    images: ['/alarm-twitter-image.png'],
  },
  alternates: {
    canonical: '/alarm',
  },
};
*/
export const metadata: Metadata = {
  title: "Online Alarm Clock - No Sleep in Fullscreen, Multi Alarms",
  description: "Best alarm clock online! Set multiple wake-up alarms with custom sounds. Fullscreen mode keeps your screen awake—never miss an alarm or reminder.",
  alternates: {
    canonical: '/alarm',
  },
};

export default function AlarmLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
