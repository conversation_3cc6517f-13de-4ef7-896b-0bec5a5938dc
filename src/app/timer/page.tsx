import { MainLayout } from "@/components/layout/main-layout";
import { TimerSEOContent } from "@/components/seo/timer-seo-content";
import { StructuredData } from "@/components/seo/structured-data";
import { TimerClient } from "./timer-client";

export default function TimerPage() {
  return (
    <MainLayout>
      <StructuredData type="timer" />

      {/* Full-height timer section */}
      <section className="relative" style={{ height: 'calc(100vh - 4rem)', overflow: 'hidden' }}>
        <TimerClient />
      </section>

      {/* SEO content section - below the fold, always rendered for SEO */}
      <section className="relative z-9" data-seo-content>
        <TimerSEOContent />
      </section>
    </MainLayout>
  );
}
