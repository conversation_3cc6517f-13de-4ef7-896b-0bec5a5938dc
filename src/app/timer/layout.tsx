import type { Metadata } from "next";

/*
export const metadata: Metadata = {
  title: "Online Timer - No Sleep in Fullscreen, Custom Alerts",
  description: "Best countdown timer online! Perfect for cooking, workouts, study & productivity. Fullscreen mode keeps your screen awake—never miss the end of your timer.",
  openGraph: {
    title: "Online Timer - Countdown Timer with Custom Alerts (No Sleep in Fullscreen)",
    description: "Best countdown timer online! Perfect for cooking, workouts, study & productivity. Fullscreen mode keeps your screen awake—never miss the end of your timer.",
    url: 'https://bestonlineclock.com/timer',
    images: [
      {
        url: '/timer-og-image.png',
        width: 1200,
        height: 630,
        alt: 'Online Timer Interface',
      },
    ],
  },
  twitter: {
    title: "Online Timer - Countdown Timer with Custom Alerts (No Sleep in Fullscreen)",
    description: "Best countdown timer online! Perfect for cooking, workouts, study & productivity. Fullscreen mode keeps your screen awake—never miss the end of your timer.",
    images: ['/timer-twitter-image.png'],
  },
  alternates: {
    canonical: '/timer',
  },
};
*/
export const metadata: Metadata = {
  title: "Online Timer - No Sleep in Fullscreen, Custom Alerts",
  description: "Best countdown timer online! Perfect for cooking, workouts, study & productivity. Fullscreen mode keeps your screen awake—never miss the end of your timer.",
  alternates: {
    canonical: '/timer',
  },
};

export default function TimerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
