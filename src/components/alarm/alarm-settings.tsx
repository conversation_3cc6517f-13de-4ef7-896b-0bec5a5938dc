"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { AlarmForm } from "./alarm-form";
import { Settings, X, AlarmClock, Info } from "lucide-react";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
}

interface AlarmSettingsProps {
  onAddAlarm: (alarm: Alarm) => boolean;
  onTestAlarm: (alarm: Omit<Alarm, 'id' | 'enabled'>) => void;
  existingAlarms: Alarm[];
}

export function AlarmSettings({
  onAddAlarm,
  onTestAlarm,
  existingAlarms
}: AlarmSettingsProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Set mounted state and detect mobile
  useEffect(() => {
    setIsMounted(true);

    const updateMobileState = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      // Auto collapse on mobile
      if (mobile) {
        setIsCollapsed(true);
      }
    };

    updateMobileState();
    window.addEventListener('resize', updateMobileState);

    return () => {
      window.removeEventListener('resize', updateMobileState);
    };
  }, []);

  // Handle fullscreen changes (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    const handleFullScreenChange = () => {
      if (document.fullscreenElement) {
        setIsCollapsed(true);
      }
    };

    document.addEventListener('fullscreenchange', handleFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
    };
  }, [isMounted]);

  const mobileTabsConfig = [
    {
      id: 0,
      label: "Alarm",
      icon: AlarmClock,
      color: "text-blue-600"
    },
    {
      id: 1,
      label: "Info",
      icon: Info,
      color: "text-purple-600"
    }
  ];

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Mobile tab content components
  const renderMobileTabContent = () => {
    switch (activeTab) {
      case 0: // Alarm
        return (
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Add New Alarm</h4>
              <AlarmForm
                onAddAlarm={onAddAlarm}
                onTestAlarm={onTestAlarm}
                existingAlarms={existingAlarms}
              />
            </div>
          </div>
        );

      case 1: // Info
        return (
          <div className="space-y-4">
            <div className="bg-blue-50/50 dark:bg-blue-900/20 border border-blue-200/50 dark:border-blue-800/50 rounded-2xl p-4">
              <div className="flex items-start space-x-3">
                <div className="text-blue-600 dark:text-blue-400 mt-0.5">
                  <Info size={18} />
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2">
                    Clock Styling
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300 leading-relaxed">
                    To customize clock appearance (colors, fonts, background), go to the{' '}
                    <Link href="/" className="underline hover:no-underline font-medium">Time page</Link>.
                    Alarm page inherits all clock settings from Time page.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* Floating Settings Button */}
      {isCollapsed && (
        <div
          className="group"
          style={{
            position: 'fixed',
            bottom: '24px',
            right: '24px',
            zIndex: 50,
          }}
        >
          <button
            onClick={toggleCollapse}
            className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group-hover:scale-110"
          >
            <Settings size={22} className="transition-transform duration-300 group-hover:rotate-90" />
          </button>
        </div>
      )}

      {/* Settings Panel */}
      {!isCollapsed && (
        <div
          className={`${
            isMobile 
              ? 'bg-white/50 dark:bg-gray-900/50 backdrop-blur-md border border-white/30 dark:border-gray-700/30 shadow-xl w-full h-[50vh] rounded-t-3xl'
              : 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border border-white/20 dark:border-gray-700/50 shadow-2xl w-96 h-[90vh] rounded-3xl'
          } flex flex-col`}
          style={{
            position: 'fixed',
            zIndex: 50,
            ...(isMobile ? {
              bottom: '0px',
              left: '0px',
              right: '0px',
            } : {
              bottom: '24px',
              right: '24px',
            }),
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Settings size={16} className="text-white" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Alarm Settings
              </h2>
            </div>
            <button
              onClick={toggleCollapse}
              className="w-8 h-8 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center justify-center transition-colors duration-200"
            >
              <X size={18} className="text-gray-500 dark:text-gray-400" />
            </button>
          </div>

          {/* Mobile Tabs */}
          {isMobile && (
            <div className="flex border-b border-gray-200/50 dark:border-gray-700/50">
              {mobileTabsConfig.map((tab) => {
                const IconComponent = tab.icon;
                const isActive = activeTab === tab.id;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 flex flex-col items-center gap-1 py-3 px-2 transition-all duration-200 ${
                      isActive
                        ? 'bg-blue-50 dark:bg-blue-900/20 border-b-2 border-blue-500'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
                    }`}
                  >
                    <IconComponent 
                      size={16} 
                      className={isActive ? 'text-blue-600' : 'text-gray-500 dark:text-gray-400'} 
                    />
                    <span className={`text-xs font-medium ${
                      isActive ? 'text-blue-600' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {tab.label}
                    </span>
                  </button>
                );
              })}
            </div>
          )}

          {/* Content */}
          {isMobile ? (
            // Mobile tabbed content
            <div className="flex-1 overflow-y-auto p-6">
              {renderMobileTabContent()}
            </div>
          ) : (
            // Desktop content
            <div className="flex-1 overflow-y-auto p-6 space-y-8">
              
              {/* Alarm Settings */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <AlarmClock size={18} className="text-blue-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Alarm</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Add New Alarm</h4>
                    <AlarmForm
                      onAddAlarm={onAddAlarm}
                      onTestAlarm={onTestAlarm}
                      existingAlarms={existingAlarms}
                    />
                  </div>
                </div>
              </div>

              {/* Information */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Info size={18} className="text-purple-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Information</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4">
                  <div className="bg-blue-50/50 dark:bg-blue-900/20 border border-blue-200/50 dark:border-blue-800/50 rounded-xl p-4">
                    <div className="flex items-start space-x-3">
                      <div className="text-blue-600 dark:text-blue-400 mt-0.5">
                        <Info size={18} />
                      </div>
                      <div>
                        <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2">
                          Clock Styling
                        </h4>
                        <p className="text-sm text-blue-700 dark:text-blue-300 leading-relaxed">
                          To customize clock appearance (colors, fonts, background), go to the{' '}
                          <Link href="/" className="underline hover:no-underline font-medium">Time page</Link>.
                          Alarm page inherits all clock settings from Time page.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className={`border-t border-gray-200/50 dark:border-gray-700/50 ${isMobile ? 'p-4' : 'p-6'}`}>
            <button
              onClick={toggleCollapse}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Done
            </button>
          </div>
        </div>
      )}
    </>
  );
}
