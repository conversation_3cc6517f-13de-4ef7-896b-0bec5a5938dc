"use client";

import { Clock, Volume2, VolumeX, Trash2 } from "lucide-react";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
}

interface AlarmDisplayProps {
  alarms: Alarm[];
  onToggleAlarm: (id: string) => void;
  onDeleteAlarm: (id: string) => void;
}

export function AlarmDisplay({ alarms, onToggleAlarm, onDeleteAlarm }: AlarmDisplayProps) {
  if (alarms.length === 0) {
    return (
      <div className="mt-8 max-w-2xl mx-auto">
        <div className="bg-white/20 dark:bg-gray-900/20 backdrop-blur-sm rounded-lg p-6 text-center">
          <Clock className="mx-auto mb-4 text-gray-400" size={48} />
          <p className="text-gray-600 dark:text-gray-400">
            No alarms set. Use the settings panel to add your first alarm.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 max-w-2xl mx-auto">
      <div className="bg-white/20 dark:bg-gray-900/20 backdrop-blur-sm rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4 text-center text-gray-800 dark:text-gray-200">
          Your Alarms
        </h3>
        
        <div className="space-y-3">
          {alarms.map((alarm) => (
            <div
              key={alarm.id}
              className={`flex items-center justify-between p-4 rounded-lg transition-all duration-200 ${
                alarm.enabled
                  ? 'bg-white/30 dark:bg-gray-800/30 border border-blue-200/50 dark:border-blue-700/50'
                  : 'bg-gray-100/30 dark:bg-gray-700/30 border border-gray-200/50 dark:border-gray-600/50'
              }`}
            >
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Clock size={20} className={alarm.enabled ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400'} />
                  <span className={`text-xl font-mono font-bold ${
                    alarm.enabled ? 'text-gray-800 dark:text-gray-200' : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {alarm.time}
                  </span>
                </div>
                
                <div>
                  <p className={`font-medium ${
                    alarm.enabled ? 'text-gray-800 dark:text-gray-200' : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {alarm.title}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Sound: {alarm.sound}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => onToggleAlarm(alarm.id)}
                  className={`p-2 rounded-full transition-colors ${
                    alarm.enabled
                      ? 'bg-blue-100 hover:bg-blue-200 text-blue-600 dark:bg-blue-900/50 dark:hover:bg-blue-800/50 dark:text-blue-400'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-400 dark:bg-gray-700/50 dark:hover:bg-gray-600/50'
                  }`}
                  title={alarm.enabled ? 'Disable alarm' : 'Enable alarm'}
                >
                  {alarm.enabled ? <Volume2 size={16} /> : <VolumeX size={16} />}
                </button>
                
                <button
                  onClick={() => onDeleteAlarm(alarm.id)}
                  className="p-2 rounded-full bg-red-100 hover:bg-red-200 text-red-600 dark:bg-red-900/50 dark:hover:bg-red-800/50 dark:text-red-400 transition-colors"
                  title="Delete alarm"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {alarms.filter(alarm => alarm.enabled).length} of {alarms.length} alarms enabled
          </p>
        </div>
      </div>
    </div>
  );
}
