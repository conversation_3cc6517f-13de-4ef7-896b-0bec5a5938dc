"use client";

import { Button } from "@/components/ui/button";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
}

interface AlarmListProps {
  alarms: Alarm[];
  onToggleAlarm: (id: string) => void;
  onDeleteAlarm: (id: string) => void;
}

export function AlarmList({ alarms, onToggleAlarm, onDeleteAlarm }: AlarmListProps) {
  if (alarms.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">No alarms set</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {alarms.map((alarm) => (
        <div
          key={alarm.id}
          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div>
            <div className="text-xl font-semibold">{alarm.time}</div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {alarm.title} • {alarm.sound}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onToggleAlarm(alarm.id)}
              className={`w-12 h-6 rounded-full p-1 transition-colors ${
                alarm.enabled ? "bg-green-500" : "bg-gray-300 dark:bg-gray-600"
              }`}
            >
              <div
                className={`bg-white w-4 h-4 rounded-full shadow-md transform transition-transform ${
                  alarm.enabled ? "translate-x-6" : "translate-x-0"
                }`}
              />
            </button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteAlarm(alarm.id)}
              className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
            >
              Delete
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
}
