"use client";

import { Minimize } from "lucide-react";
import { exitFullscreen } from "@/lib/fullscreen";

interface FullscreenExitButtonProps {
  isVisible: boolean;
}

export function FullscreenExitButton({ isVisible }: FullscreenExitButtonProps) {
  if (!isVisible) return null;

  const handleExit = async () => {
    await exitFullscreen();
  };

  return (
    <button
      onClick={handleExit}
      className="fixed top-4 right-4 z-50 w-12 h-12 bg-black/20 hover:bg-black/40 backdrop-blur-sm border border-white/10 rounded-full flex items-center justify-center transition-all duration-300 group"
      aria-label="Exit fullscreen"
    >
      <Minimize className="h-6 w-6 text-white group-hover:scale-110 transition-transform duration-200" />
    </button>
  );
}
