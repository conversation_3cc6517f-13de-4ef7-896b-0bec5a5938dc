import Link from 'next/link';
import { ChevronRight, Home, Clock } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className = "" }: BreadcrumbProps) {
  return (
    <nav className={`flex items-center space-x-2 text-sm ${className}`} aria-label="Breadcrumb">
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && (
            <ChevronRight className="w-4 h-4 text-gray-400 dark:text-gray-500 mx-2" />
          )}
          
          {item.href ? (
            <Link
              href={item.href}
              className="flex items-center space-x-1 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              {item.icon && <span className="w-4 h-4">{item.icon}</span>}
              <span>{item.label}</span>
            </Link>
          ) : (
            <div className="flex items-center space-x-1 text-gray-900 dark:text-white font-medium">
              {item.icon && <span className="w-4 h-4">{item.icon}</span>}
              <span>{item.label}</span>
            </div>
          )}
        </div>
      ))}
    </nav>
  );
}

// Predefined breadcrumb configurations
export const breadcrumbConfigs = {
  cityTime: (cityName: string, countryFlag: string) => [
    {
      label: "Home",
      href: "/",
      icon: <Home className="w-4 h-4" />
    },
    {
      label: "World Time",
      href: "/time",
      icon: <Clock className="w-4 h-4" />
    },
    {
      label: `${countryFlag} ${cityName}`,
      icon: null
    }
  ]
};
