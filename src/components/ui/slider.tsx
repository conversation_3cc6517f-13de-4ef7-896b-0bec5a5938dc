import * as React from "react";
import { cn } from "@/lib/utils";

interface SliderProps {
  value: number[];
  min: number;
  max: number;
  step: number;
  onValueChange: (value: number[]) => void;
  className?: string;
}

const Slider = React.forwardRef<HTMLDivElement, SliderProps>(
  ({ value, min, max, step, onValueChange, className }, ref) => {
    const [isDragging, setIsDragging] = React.useState(false);
    const trackRef = React.useRef<HTMLDivElement>(null);

    const currentValue = value[0] || min;
    const percentage = ((currentValue - min) / (max - min)) * 100;

    const handleStart = (e: React.MouseEvent | React.TouchEvent) => {
      e.preventDefault();
      setIsDragging(true);
      updateValue(e);
    };

    const handleMove = React.useCallback((e: MouseEvent | TouchEvent) => {
      if (isDragging) {
        updateValue(e);
      }
    }, [isDragging]);

    const handleEnd = React.useCallback(() => {
      setIsDragging(false);
    }, []);

    const updateValue = (e: MouseEvent | TouchEvent | React.MouseEvent | React.TouchEvent) => {
      if (!trackRef.current) return;

      const rect = trackRef.current.getBoundingClientRect();
      const clientX = 'touches' in e ? e.touches[0]?.clientX || e.changedTouches[0]?.clientX : e.clientX;
      const x = clientX - rect.left;
      const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
      const newValue = min + (percentage / 100) * (max - min);
      const steppedValue = Math.round(newValue / step) * step;
      const clampedValue = Math.max(min, Math.min(max, steppedValue));

      onValueChange([clampedValue]);
    };

    React.useEffect(() => {
      if (isDragging) {
        document.addEventListener('mousemove', handleMove);
        document.addEventListener('mouseup', handleEnd);
        document.addEventListener('touchmove', handleMove);
        document.addEventListener('touchend', handleEnd);
        return () => {
          document.removeEventListener('mousemove', handleMove);
          document.removeEventListener('mouseup', handleEnd);
          document.removeEventListener('touchmove', handleMove);
          document.removeEventListener('touchend', handleEnd);
        };
      }
    }, [isDragging, handleMove, handleEnd]);

    return (
      <div ref={ref} className={cn("relative w-full py-2", className)}>
        <div
          ref={trackRef}
          className="relative h-3 w-full bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer"
          onMouseDown={handleStart}
          onTouchStart={handleStart}
        >
          <div
            className="absolute h-full bg-blue-600 dark:bg-blue-500 rounded-full"
            style={{ width: `${percentage}%` }}
          />
          <div
            className="absolute h-6 w-6 bg-white dark:bg-gray-900 border-2 border-blue-600 dark:border-blue-500 rounded-full shadow-lg cursor-grab active:cursor-grabbing hover:scale-105 transition-transform"
            style={{
              left: `${percentage}%`,
              top: '50%',
              transform: 'translate(-50%, -50%)',
            }}
            onMouseDown={handleStart}
            onTouchStart={handleStart}
          />
        </div>
      </div>
    );
  }
);
Slider.displayName = "Slider";

export { Slider };
