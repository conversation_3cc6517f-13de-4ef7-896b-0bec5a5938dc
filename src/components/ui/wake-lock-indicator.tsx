import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

interface WakeLockIndicatorProps {
  isFullScreen: boolean;
  isSupported: boolean;
  isActive: boolean;
  className?: string;
}

export function WakeLockIndicator({ 
  isFullScreen, 
  isSupported, 
  isActive, 
  className = "" 
}: WakeLockIndicatorProps) {
  const [showSuccess, setShowSuccess] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  // Show success message briefly when wake lock is activated
  useEffect(() => {
    if (isActive && isSupported) {
      setShowSuccess(true);
      const timer = setTimeout(() => {
        setShowSuccess(false);
      }, 3000); // Show for 3 seconds
      
      return () => clearTimeout(timer);
    }
  }, [isActive, isSupported]);

  // Reset dismissed state when fullscreen changes
  useEffect(() => {
    if (!isFullScreen) {
      setDismissed(false);
    }
  }, [isFullScreen]);

  if (!isFullScreen) return null;

  // Don't show anything if user has dismissed the warning
  if (dismissed) return null;

  // Show success message briefly
  if (showSuccess && isSupported && isActive) {
    return (
      <div className={`fixed top-4 left-4 z-[60] ${className}`}>
        <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-green-500/90 text-white animate-in fade-in duration-300">
          <div className="w-2 h-2 rounded-full bg-white animate-pulse" />
          <span>🔒 Screen Wake Lock Activated</span>
        </div>
      </div>
    );
  }

  // Show persistent warning for inactive or unsupported states
  if (!isActive || !isSupported) {
    return (
      <div className={`fixed top-4 left-4 z-[60] ${className}`}>
        <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-yellow-500/90 text-black">
          <span>⚠️</span>
          <span>
            {!isSupported 
              ? 'Screen Wake Lock not supported in this browser' 
              : '⏸️ Screen Wake Lock inactive'
            }
          </span>
          <button
            onClick={() => setDismissed(true)}
            className="ml-2 p-0.5 hover:bg-black/10 rounded transition-colors"
            aria-label="Dismiss"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return null;
} 