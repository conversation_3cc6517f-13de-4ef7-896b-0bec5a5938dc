"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { formatTimeWithMilliseconds } from "@/lib/utils";
import { Download, Trash2 } from "lucide-react";

interface Lap {
  id: number;
  time: number;
  total: number;
}

interface StopwatchDisplayProps {
  timeFormat?: string;
  textColor?: string;
  fontSize?: string;
  fontFamily?: string;
  position?: { x: number; y: number };
}

export function StopwatchDisplay({
  timeFormat = "00:00.00",
  textColor = "#000000",
  fontSize = "6rem",
  fontFamily = "monospace",
  position = { x: 0, y: 0 },
}: StopwatchDisplayProps) {
  const [time, setTime] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [laps, setLaps] = useState<Lap[]>([]);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const lastLapTimeRef = useRef<number>(0);

  useEffect(() => {
    if (isRunning) {
      startTimeRef.current = Date.now() - time;
      intervalRef.current = setInterval(() => {
        setTime(Date.now() - startTimeRef.current);
      }, 10);
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, time]);

  const handleStart = () => {
    setIsRunning(true);
  };

  const handlePause = () => {
    setIsRunning(false);
  };

  const handleReset = () => {
    setIsRunning(false);
    setTime(0);
    setLaps([]);
    lastLapTimeRef.current = 0;
  };

  const handleLap = () => {
    const lapTime = time - lastLapTimeRef.current;
    lastLapTimeRef.current = time;

    setLaps((prevLaps) => [
      {
        id: prevLaps.length + 1,
        time: lapTime,
        total: time,
      },
      ...prevLaps,
    ]);
  };

  const handleClearLaps = () => {
    setLaps([]);
    lastLapTimeRef.current = 0;
  };

  const handleExportCSV = () => {
    if (laps.length === 0) {
      alert("No lap data to export!");
      return;
    }

    // Create CSV content
    const headers = ["Lap", "Lap Time", "Total Time"];
    const csvContent = [
      headers.join(","),
      ...laps.map((lap, index) => [
        laps.length - index,
        `"${formatTimeWithMilliseconds(lap.time, timeFormat)}"`,
        `"${formatTimeWithMilliseconds(lap.total, timeFormat)}"`
      ].join(","))
    ].join("\n");

    // Create and download file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `stopwatch-laps-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div
      className="flex flex-col items-center w-full h-full relative px-4 py-4 pt-8 sm:pt-4"
      style={{
        transform: `translate(${position.x}px, ${position.y}px)`,
        transition: 'transform 0.3s ease',
      }}
    >
      {/* Fixed timer and controls container */}
      <div className="flex flex-col items-center w-full max-w-2xl mt-4 sm:mt-0" style={{ backgroundColor: 'inherit' }}>
        {/* Timer display */}
        <div
          className="mb-6 text-center select-none"
          style={{
            fontSize: fontSize,
            fontFamily: fontFamily,
            color: textColor,
            lineHeight: '1.1',
          }}
        >
          {formatTimeWithMilliseconds(time, timeFormat)}
        </div>

        {/* Button controls */}
        <div className="flex flex-col items-center space-y-4 mb-6">
          {!isRunning ? (
            <div className="flex items-center space-x-4 sm:space-x-6">
              {/* Start/Resume Button - Large and prominent */}
              <Button
                onClick={handleStart}
                className="px-8 py-3 sm:px-12 sm:py-4 text-lg sm:text-xl font-semibold bg-green-600 hover:bg-green-700 text-white border-0 rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95"
              >
                {time > 0 ? 'Resume' : 'Start'}
              </Button>

              {/* Reset Button - Secondary, only show if there's data */}
              {(time > 0 || laps.length > 0) && (
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="px-4 py-2 sm:px-6 sm:py-3 text-base sm:text-lg bg-white/80 dark:bg-gray-800/80 border-2 border-gray-400 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 hover:border-gray-600 dark:hover:border-gray-500 hover:text-gray-900 dark:hover:text-gray-100 rounded-xl transition-all duration-200"
                >
                  Reset
                </Button>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-3">
              {/* Pause Button - Top row */}
              <Button
                variant="outline"
                onClick={handlePause}
                className="px-6 py-2 sm:px-8 sm:py-3 text-base sm:text-lg bg-orange-50/80 dark:bg-orange-900/20 border-2 border-orange-400 dark:border-orange-600 text-orange-600 dark:text-orange-400 hover:bg-orange-50 dark:hover:bg-orange-900/30 hover:border-orange-600 dark:hover:border-orange-500 hover:text-orange-700 dark:hover:text-orange-300 rounded-xl transition-all duration-200"
              >
                Pause
              </Button>

              {/* Lap Button - Bottom row, larger for frequent use */}
              <Button
                onClick={handleLap}
                className="px-12 py-4 sm:px-16 sm:py-6 text-xl sm:text-2xl font-bold bg-blue-600 hover:bg-blue-700 text-white border-0 rounded-2xl shadow-xl transition-all duration-200 transform hover:scale-105 active:scale-95"
              >
                Lap
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Scrollable laps section */}
      {laps.length > 0 && (
        <div className="w-full max-w-2xl flex-1 flex flex-col min-h-0">
          <h3 className="text-lg font-medium mb-3 text-center" style={{ color: textColor }}>
            Laps ({laps.length})
          </h3>
          
          <div className="border rounded-lg overflow-hidden flex-1 min-h-0" style={{ backgroundColor: 'rgba(255,255,255,0.05)' }}>
            <div className="overflow-y-auto h-full">
              <table className="w-full">
                <thead 
                  className="sticky top-0 backdrop-blur-md" 
                  style={{ 
                    backgroundColor: 'rgba(255,255,255,0.1)', 
                    backdropFilter: 'blur(8px)',
                    borderBottom: '1px solid rgba(255,255,255,0.2)'
                  }}
                >
                  <tr>
                    <th className="py-3 px-3 sm:px-4 text-left text-sm font-medium" style={{ color: textColor }}>
                      Lap
                    </th>
                    <th className="py-3 px-3 sm:px-4 text-left text-sm font-medium" style={{ color: textColor }}>
                      Time
                    </th>
                    <th className="py-3 px-3 sm:px-4 text-left text-sm font-medium" style={{ color: textColor }}>
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {laps.map((lap, index) => (
                    <tr key={lap.id} className="border-t border-gray-200 dark:border-gray-700">
                      <td className="py-2 px-3 sm:px-4 text-sm" style={{ color: textColor }}>
                        {laps.length - index}
                      </td>
                      <td className="py-2 px-3 sm:px-4 text-sm font-mono" style={{ color: textColor }}>
                        {formatTimeWithMilliseconds(lap.time, timeFormat)}
                      </td>
                      <td className="py-2 px-3 sm:px-4 text-sm font-mono" style={{ color: textColor }}>
                        {formatTimeWithMilliseconds(lap.total, timeFormat)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Export and Clear buttons */}
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mt-4 justify-center">
            <Button
              variant="outline"
              onClick={handleExportCSV}
              className="flex items-center justify-center space-x-2 text-sm bg-white/80 dark:bg-gray-800/80 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100"
            >
              <Download size={16} />
              <span>Export CSV</span>
            </Button>
            <Button
              variant="outline"
              onClick={handleClearLaps}
              className="flex items-center justify-center space-x-2 text-sm bg-red-50/80 dark:bg-red-900/20 border-red-300 dark:border-red-700 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 hover:text-red-700 dark:hover:text-red-300"
            >
              <Trash2 size={16} />
              <span>Clear Data</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
