"use client";

import { But<PERSON> } from "@/components/ui/button";

interface TimerFormProps {
  onSetTimer: (time: number) => void;
}

export function TimerForm({ onSetTimer }: TimerFormProps) {

  const presetTimes = [
    { label: "1 min", time: 60 * 1000 },
    { label: "5 min", time: 5 * 60 * 1000 },
    { label: "10 min", time: 10 * 60 * 1000 },
    { label: "15 min", time: 15 * 60 * 1000 },
    { label: "30 min", time: 30 * 60 * 1000 },
    { label: "1 hour", time: 60 * 60 * 1000 },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4 text-center">Quick Start Timer</h3>
        <div className="grid grid-cols-2 gap-3">
          {presetTimes.map((preset) => (
            <Button
              key={preset.label}
              variant="outline"
              onClick={() => onSetTimer(preset.time)}
              className="w-full py-3 text-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
            >
              {preset.label}
            </Button>
          ))}
        </div>
      </div>

      <div className="text-center text-sm text-gray-500 dark:text-gray-400">
        For custom times, use the settings panel →
      </div>
    </div>
  );
}
