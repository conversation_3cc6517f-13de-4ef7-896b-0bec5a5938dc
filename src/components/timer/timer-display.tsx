"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Pause, RotateCcw } from "lucide-react";

interface TimerDisplayProps {
  initialTime: number; // in milliseconds
  originalTime?: number; // Original timer duration for progress calculation
  onComplete: () => void;
  sound?: string; // Sound to play when timer completes
  position?: { x: number; y: number }; // Position offset
  startTime?: number; // When the timer was started (timestamp)
  isRunning?: boolean; // Whether the timer should be running
  onStart?: () => void; // Callback when start is clicked
  onPause?: () => void; // Callback when pause is clicked
  onReset?: () => void; // Callback when reset is clicked
}

export function TimerDisplay({
  initialTime,
  originalTime,
  onComplete,
  sound = "default",
  position = { x: 0, y: 0 },
  startTime = 0,
  isRunning: externalIsRunning = false,
  onStart,
  onPause,
  onReset
}: TimerDisplayProps) {
  const [timeLeft, setTimeLeft] = useState(initialTime);
  const [isRunning, setIsRunning] = useState(externalIsRunning);
  const [progress, setProgress] = useState(100);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const completedRef = useRef(false);

  // Use useCallback to prevent unnecessary re-renders
  const handleComplete = useCallback(() => {
    if (!completedRef.current) {
      completedRef.current = true;

      // Play sound when timer completes
      if (audioRef.current) {
        audioRef.current.loop = true; // 启用循环播放
        audioRef.current.play().catch((error) => {
          console.error("Failed to play timer sound:", error);
        });
      }

      // Use setTimeout to avoid calling setState during render
      setTimeout(() => {
        onComplete();
      }, 0);
    }
  }, [onComplete]);

  // Stop audio when component unmounts or timer is reset
  useEffect(() => {
    const audioElement = audioRef.current;
    return () => {
      if (audioElement) {
        audioElement.pause();
        audioElement.currentTime = 0;
        audioElement.loop = false; // 禁用循环播放
      }
    };
  }, []);

  // Stop audio when timer is reset
  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current.loop = false; // 禁用循环播放
    }
  };

  // Calculate time left based on start time if provided
  useEffect(() => {
    if (startTime > 0 && externalIsRunning) {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, initialTime - elapsed);
      setTimeLeft(remaining);
      setIsRunning(true);

      if (remaining <= 0 && !completedRef.current) {
        handleComplete();
      }
    } else if (initialTime > 0) {
      setTimeLeft(initialTime);
      setIsRunning(externalIsRunning);
      completedRef.current = false;
    } else {
      // If initialTime is 0, reset everything
      setTimeLeft(0);
      setIsRunning(false);
      completedRef.current = false;
    }

    // Use originalTime for progress calculation if available, otherwise use initialTime
    const progressBase = originalTime || initialTime;
    if (progressBase > 0) {
      setProgress((timeLeft / progressBase) * 100);
    } else {
      setProgress(100);
    }
  }, [initialTime, startTime, externalIsRunning, handleComplete, timeLeft, originalTime]);

  useEffect(() => {
    if (isRunning && timeLeft > 0 && !completedRef.current) {
      intervalRef.current = setInterval(() => {
        if (startTime > 0) {
          // Calculate based on elapsed time from start
          const elapsed = Date.now() - startTime;
          const remaining = Math.max(0, initialTime - elapsed);
          setTimeLeft(remaining);

          if (remaining <= 0 && !completedRef.current) {
            setIsRunning(false);
            handleComplete();
          }
        } else {
          // Use countdown approach
          setTimeLeft((prevTime) => {
            const newTime = prevTime - 1000;
            if (newTime <= 0 && !completedRef.current) {
              setIsRunning(false);
              handleComplete();
              return 0;
            }
            return newTime;
          });
        }
      }, 1000);
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, timeLeft, handleComplete, startTime, initialTime]);

  useEffect(() => {
    const progressBase = originalTime || initialTime;
    if (progressBase > 0) {
      setProgress((timeLeft / progressBase) * 100);
    }
  }, [timeLeft, initialTime, originalTime]);

  const handleStart = () => {
    if (timeLeft > 0) {
      if (onStart) {
        onStart(); // Let parent handle the state
      } else {
        setIsRunning(true); // Fallback to local state
      }
    }
  };

  const handlePause = () => {
    if (onPause) {
      onPause(); // Let parent handle the state
    } else {
      setIsRunning(false); // Fallback to local state
    }
  };

  const handleReset = () => {
    if (onReset) {
      onReset(); // Let parent handle the state
    } else {
      // Fallback to local state
      setIsRunning(false);
      setTimeLeft(initialTime);
      setProgress(100);
      completedRef.current = false;
    }
    stopAudio(); // Always stop audio
  };

  // Calculate time components for display
  const totalSeconds = Math.ceil(timeLeft / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  return (
    <div
      className="flex flex-col items-center justify-center w-full h-full relative"
      style={{
        transform: `translate(${position.x}px, ${position.y}px)`,
        transition: 'transform 0.3s ease',
      }}
    >
      <audio ref={audioRef} src={`https://static.bestonlineclock.com/sounds/${sound}`} />

      {/* Modern Card-style Timer Display */}
      <div className="relative w-full max-w-2xl bg-gradient-to-br from-blue-50/40 to-indigo-100/40 dark:from-gray-800/40 dark:to-gray-900/40 rounded-3xl p-8 shadow-2xl border border-white/20 dark:border-gray-700/30 backdrop-blur-md">

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-12 overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-1000 ease-out"
            style={{ width: `${progress}%` }}
          />
        </div>

        {/* Time Display */}
        <div className="text-center mb-12">
          <div className="flex justify-center items-center space-x-4 mb-6">
            {hours > 0 && (
              <>
                <div className="bg-white/60 dark:bg-gray-800/60 rounded-2xl px-6 py-4 shadow-lg backdrop-blur-sm">
                  <div className="text-6xl font-bold text-gray-800 dark:text-white font-mono">
                    {hours.toString().padStart(2, '0')}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 uppercase tracking-wide mt-2">
                    Hours
                  </div>
                </div>
                <div className="text-4xl font-bold text-gray-400">:</div>
              </>
            )}

            <div className="bg-white/60 dark:bg-gray-800/60 rounded-2xl px-6 py-4 shadow-lg backdrop-blur-sm">
              <div className="text-6xl font-bold text-gray-800 dark:text-white font-mono">
                {minutes.toString().padStart(2, '0')}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400 uppercase tracking-wide mt-2">
                Minutes
              </div>
            </div>

            <div className="text-4xl font-bold text-gray-400">:</div>

            <div className="bg-white/60 dark:bg-gray-800/60 rounded-2xl px-6 py-4 shadow-lg backdrop-blur-sm">
              <div className="text-6xl font-bold text-gray-800 dark:text-white font-mono">
                {seconds.toString().padStart(2, '0')}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400 uppercase tracking-wide mt-2">
                Seconds
              </div>
            </div>
          </div>

          {/* Status Text */}
          <div className="text-lg text-gray-600 dark:text-gray-400 font-medium">
            {timeLeft === 0 ? 'Timer Complete!' : isRunning ? 'Timer Running...' : 'Timer Paused'}
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex justify-center space-x-4">
          {!isRunning ? (
            <Button
              onClick={handleStart}
              disabled={timeLeft === 0}
              className="px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              <Play size={20} className="mr-2" />
              Start
            </Button>
          ) : (
            <Button
              onClick={handlePause}
              className="px-8 py-3 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105"
            >
              <Pause size={20} className="mr-2" />
              Pause
            </Button>
          )}

          <Button
            variant="outline"
            onClick={handleReset}
            className="px-8 py-3 border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105"
          >
            <RotateCcw size={20} className="mr-2" />
            Reset
          </Button>
        </div>
      </div>
    </div>
  );
}
