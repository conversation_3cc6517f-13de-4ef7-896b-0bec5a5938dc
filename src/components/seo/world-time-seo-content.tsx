"use client";

import Link from 'next/link';
import { Footer } from './Footer';
import { getAllCities } from '@/lib/timezone';
import { getCountryFlag } from '@/lib/country-flags';
import { useState, useMemo } from 'react';

export function WorldTimeSEOContent() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('');
  
  const allCities = getAllCities();
  
  // Group cities by country
  const citiesByCountry = useMemo(() => {
    const grouped = allCities.reduce((acc, city) => {
      if (!acc[city.country]) {
        acc[city.country] = [];
      }
      acc[city.country].push(city);
      return acc;
    }, {} as Record<string, typeof allCities>);
    
    // Sort countries alphabetically
    const sortedCountries = Object.keys(grouped).sort();
    const result: Record<string, typeof allCities> = {};
    sortedCountries.forEach(country => {
      result[country] = grouped[country].sort((a, b) => a.name.localeCompare(b.name));
    });
    
    return result;
  }, [allCities]);

  // Filter cities based on search term and selected country
  const filteredCities = useMemo(() => {
    let filtered = allCities;
    
    if (selectedCountry) {
      filtered = filtered.filter(city => city.country === selectedCountry);
    }
    
    if (searchTerm) {
      filtered = filtered.filter(city => 
        city.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        city.country.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    return filtered;
  }, [allCities, searchTerm, selectedCountry]);

  const countries = Object.keys(citiesByCountry);

  return (
    <div className="w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-4 pt-20 pb-12 space-y-12">
        {/* Hero Section */}
        <section className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
            World Time Zones - Current Time Around the Globe
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
            Check the current time in major cities worldwide. Our comprehensive world clock displays
            accurate local time for international business, travel planning, and staying connected
            across time zones. <strong>Compare up to 3 cities simultaneously</strong> with our multi-city
            time display feature for efficient global coordination.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <span>✓ Real-time Updates</span>
            <span>✓ {allCities.length} Major Cities</span>
            <span>✓ Multi-City Display</span>
            <span>✓ All Time Zones</span>
            <span>✓ Accurate & Reliable</span>
          </div>
        </section>

        {/* Multi-City Feature Highlight */}
        <section className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            🌍 Multi-City Time Display - Compare Times Instantly
          </h2>
          <div className="max-w-4xl mx-auto">
            <p className="text-lg text-gray-600 dark:text-gray-300 text-center mb-8">
              Our unique multi-city feature lets you monitor up to 3 different time zones simultaneously
              alongside your local time. Perfect for global teams, international business, and travel coordination.
            </p>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                  💼 Business Coordination
                </h3>
                <ul className="text-gray-600 dark:text-gray-300 space-y-2 text-sm">
                  <li>• Schedule meetings across time zones</li>
                  <li>• Coordinate with global teams</li>
                  <li>• Track market opening hours</li>
                  <li>• Plan international conference calls</li>
                  <li>• Monitor customer service hours</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                  ✈️ Travel & Events
                </h3>
                <ul className="text-gray-600 dark:text-gray-300 space-y-2 text-sm">
                  <li>• Track flight departure/arrival times</li>
                  <li>• Coordinate with family abroad</li>
                  <li>• Plan international events</li>
                  <li>• Monitor hotel check-in times</li>
                  <li>• Schedule virtual tourism</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                  📱 Digital Nomads
                </h3>
                <ul className="text-gray-600 dark:text-gray-300 space-y-2 text-sm">
                  <li>• Track client time zones</li>
                  <li>• Coordinate remote work schedules</li>
                  <li>• Plan content publishing times</li>
                  <li>• Monitor social media peak hours</li>
                  <li>• Schedule international calls</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                  🎮 Gaming & Entertainment
                </h3>
                <ul className="text-gray-600 dark:text-gray-300 space-y-2 text-sm">
                  <li>• Coordinate online gaming sessions</li>
                  <li>• Track live stream schedules</li>
                  <li>• Plan international tournaments</li>
                  <li>• Monitor game server resets</li>
                  <li>• Schedule virtual events</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                  📈 Trading & Finance
                </h3>
                <ul className="text-gray-600 dark:text-gray-300 space-y-2 text-sm">
                  <li>• Monitor global market hours</li>
                  <li>• Track forex trading sessions</li>
                  <li>• Plan cryptocurrency trades</li>
                  <li>• Coordinate with international brokers</li>
                  <li>• Schedule financial reports</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
                  👨‍👩‍👧‍👦 Family & Friends
                </h3>
                <ul className="text-gray-600 dark:text-gray-300 space-y-2 text-sm">
                  <li>• Stay connected with family abroad</li>
                  <li>• Plan international video calls</li>
                  <li>• Coordinate holiday celebrations</li>
                  <li>• Track children's school hours</li>
                  <li>• Schedule birthday calls</li>
                </ul>
              </div>
            </div>

            <div className="mt-8 text-center">
              <div className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg">
                <span className="font-medium">💡 Pro Tip: Add your most frequently used cities for instant access!</span>
              </div>
            </div>
          </div>
        </section>

        {/* Search Section */}
        <section className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            Find City Time Zones
          </h2>
          <div className="max-w-2xl mx-auto space-y-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Search by City or Country
                </label>
                <input
                  type="text"
                  id="search"
                  placeholder="e.g., New York, Tokyo, United States..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              </div>
              <div className="md:w-48">
                <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Filter by Country
                </label>
                <select
                  id="country"
                  value={selectedCountry}
                  onChange={(e) => setSelectedCountry(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="">All Countries</option>
                  {countries.map(country => (
                    <option key={country} value={country}>{country}</option>
                  ))}
                </select>
              </div>
            </div>
            
            {/* Search Results */}
            {(searchTerm || selectedCountry) && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
                  Search Results ({filteredCities.length} cities)
                </h3>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredCities.map((city) => (
                    <Link
                      key={city.id}
                      href={`/time/${city.id}`}
                      className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600"
                    >
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        {city.name}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {city.country}
                      </p>
                      <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                        {city.timezone}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        UTC{city.utcOffset >= 0 ? '+' : ''}{city.utcOffset}
                      </p>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Cities by Country */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            World Time Zones by Country
          </h2>
          <div className="space-y-8">
            {Object.entries(citiesByCountry).map(([country, cities]) => (
              <div key={country} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                <h3 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
                  <span className="mr-3 text-3xl">{getCountryFlag(country)}</span>
                  {country} ({cities.length} {cities.length === 1 ? 'city' : 'cities'})
                </h3>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {cities.map((city) => (
                    <Link
                      key={city.id}
                      href={`/time/${city.id}`}
                      className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500"
                    >
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {city.name}
                      </h4>
                      <div className="space-y-1 text-sm">
                        <p className="text-blue-600 dark:text-blue-400">
                          {city.timezone}
                        </p>
                        <p className="text-gray-600 dark:text-gray-300">
                          UTC{city.utcOffset >= 0 ? '+' : ''}{city.utcOffset}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {city.coordinates.lat}°, {city.coordinates.lng}°
                        </p>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Why Use World Time */}
        <section className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            Why Use Our World Time Clock?
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-4xl mb-4">🌐</div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Global Coverage</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Access current time for major cities across all continents and time zones.
              </p>
            </div>

            <div className="text-center">
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Real-time Updates</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Live time updates every second with automatic daylight saving time adjustments.
              </p>
            </div>

            <div className="text-center">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Easy Search</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Quickly find any city with our powerful search and country filter features.
              </p>
            </div>

            <div className="text-center">
              <div className="text-4xl mb-4">💼</div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Business Ready</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Perfect for international meetings, conference calls, and global business operations.
              </p>
            </div>

            <div className="text-center">
              <div className="text-4xl mb-4">✈️</div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Travel Planning</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Essential tool for travelers to check destination times and plan itineraries.
              </p>
            </div>

            <div className="text-center">
              <div className="text-4xl mb-4">📱</div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Mobile Friendly</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Responsive design works perfectly on all devices - desktop, tablet, and mobile.
              </p>
            </div>
          </div>
        </section>

        {/* Navigation Links */}
        <section className="text-center">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
            More Online Clock Tools
          </h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Main Clock
            </Link>
            <Link
              href="/timer"
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Online Timer
            </Link>
            <Link
              href="/alarm"
              className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Alarm Clock
            </Link>
            <Link
              href="/stopwatch"
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Stopwatch
            </Link>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
}
