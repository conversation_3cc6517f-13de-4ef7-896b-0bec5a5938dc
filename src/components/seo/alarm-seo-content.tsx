import Link from 'next/link';
import { Footer } from './Footer';

export function AlarmSEOContent() {
  return (
    <div className="w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-4 pt-20 pb-12 space-y-12">
        {/* Hero Section */}
        <section className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
            Online Alarm Clock – No Sleep in Fullscreen, Multi Alarms
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
            Set multiple alarms with custom titles, sounds, and smart snooze functionality.
            Perfect for wake-up calls, reminders, meetings, and time-sensitive tasks.
            Works instantly in your browser on any device - no downloads required.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <span>✓ Multiple Alarms</span>
            <span>✓ Custom Sounds</span>
            <span>✓ Smart Snooze</span>
            <span>✓ No Installation</span>
            <span>✓ Mobile Friendly</span>
          </div>
        </section>

        {/* Features Grid */}
        <section className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              ⏰ Multiple Alarms
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-3">
              Set unlimited alarms simultaneously. Each alarm can have a custom title,
              specific time, and individual sound selection for different purposes.
            </p>
            <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
              <li>• Unlimited simultaneous alarms</li>
              <li>• Custom alarm titles/labels</li>
              <li>• Individual enable/disable</li>
              <li>• Duplicate time prevention</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              🔊 Custom Alarm Sounds
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-3">
              Choose from 22 different alarm sounds to match your preference.
              Test each sound before setting to find the perfect wake-up tone.
            </p>
            <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
              <li>• Morning clock alarms</li>
              <li>• Classic and digital alarm sounds</li>
              <li>• Warning and emergency alerts</li>
              <li>• Gentle ringtones and nature sounds</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              😴 Smart Snooze
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-3">
              Intelligent snooze system that creates temporary alarms without
              affecting your original alarm settings. Customizable snooze duration.
            </p>
            <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
              <li>• 5-60 minute snooze options</li>
              <li>• Temporary snooze alarms</li>
              <li>• Original alarm preservation</li>
              <li>• Queue system for multiple alarms</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              🎯 Test Functionality
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-3">
              Preview your alarm sound and settings before saving. Ensure your
              alarm will work perfectly when you need it most.
            </p>
            <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
              <li>• Sound preview testing</li>
              <li>• Volume level checking</li>
              <li>• Settings verification</li>
              <li>• Instant feedback</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              📱 Mobile Optimized
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-3">
              Fully responsive design with touch-friendly controls. Settings panel
              optimized for mobile screens with easy access to all features.
            </p>
            <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
              <li>• Touch-friendly interface</li>
              <li>• Mobile settings panel</li>
              <li>• Responsive design</li>
              <li>• Cross-platform compatibility</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              💾 Persistent Storage
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-3">
              Your alarms and settings are automatically saved locally.
              Return to find all your alarms exactly as you left them.
            </p>
            <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
              <li>• Automatic alarm saving</li>
              <li>• Settings persistence</li>
              <li>• Local storage</li>
              <li>• No account required</li>
            </ul>
          </div>
        </section>

        {/* Alarm Use Cases */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Perfect Online Alarm Clock for Every Need
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
                🌅 Morning Routines
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
                <li>• Wake-up alarms with custom sounds</li>
                <li>• Multiple morning reminders</li>
                <li>• Gentle wake-up progression</li>
                <li>• Snooze for extra sleep</li>
                <li>• Weekend vs weekday schedules</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
                💼 Work & Meetings
              </h3>
              <ul className="text-sm text-green-800 dark:text-green-200 space-y-2">
                <li>• Meeting reminders</li>
                <li>• Break time alerts</li>
                <li>• Deadline notifications</li>
                <li>• Call schedule alarms</li>
                <li>• Task completion reminders</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-purple-900 dark:text-purple-100">
                💊 Health & Wellness
              </h3>
              <ul className="text-sm text-purple-800 dark:text-purple-200 space-y-2">
                <li>• Medication reminders</li>
                <li>• Meal time alerts</li>
                <li>• Exercise schedule</li>
                <li>• Water intake reminders</li>
                <li>• Sleep schedule maintenance</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-orange-900 dark:text-orange-100">
                📚 Study & Education
              </h3>
              <ul className="text-sm text-orange-800 dark:text-orange-200 space-y-2">
                <li>• Study session start times</li>
                <li>• Exam preparation alerts</li>
                <li>• Assignment deadline reminders</li>
                <li>• Class schedule alarms</li>
                <li>• Break time notifications</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-red-900 dark:text-red-100">
                🏠 Daily Life
              </h3>
              <ul className="text-sm text-red-800 dark:text-red-200 space-y-2">
                <li>• Cooking and baking timers</li>
                <li>• Laundry cycle reminders</li>
                <li>• Pet feeding schedules</li>
                <li>• Garden watering alerts</li>
                <li>• Appointment reminders</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-teal-900 dark:text-teal-100">
                🎯 Productivity
              </h3>
              <ul className="text-sm text-teal-800 dark:text-teal-200 space-y-2">
                <li>• Pomodoro technique timing</li>
                <li>• Focus session reminders</li>
                <li>• Time blocking alerts</li>
                <li>• Goal achievement milestones</li>
                <li>• Habit formation cues</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Why Choose Our Online Alarm Clock? */}
        <section className="bg-gray-50 dark:bg-gray-900 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            Why Choose Our Online Alarm Clock?
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                🚀 Instant Setup
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                No downloads, installations, or account creation required.
                Start setting alarms immediately in any web browser.
              </p>

              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                🎨 Fully Customizable
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Personalize your alarm experience with custom titles, sounds,
                and display settings. Make it work exactly how you need it.
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                📱 Universal Compatibility
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Works on all devices and operating systems. Access your alarms
                from desktop, tablet, or mobile with consistent functionality.
              </p>

              <h3 className="text-xl font-semibant text-gray-900 dark:text-white">
                🔒 Privacy Focused
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                All data stored locally on your device. No personal information
                collected or shared. Your alarms remain completely private.
              </p>
            </div>
          </div>
        </section>

        {/* Fullscreen Sleep Prevention Feature (ENGLISH) */}
        <section className="bg-yellow-50 dark:bg-yellow-900/10 rounded-lg p-6 my-8">
          <h3 className="text-xl font-bold text-yellow-800 dark:text-yellow-200 mb-2">💤 Fullscreen Sleep Prevention – Never Miss an Alarm</h3>
          <p className="text-yellow-900 dark:text-yellow-100">
            Using your computer as an alarm clock? The biggest problem is that most devices will go to sleep or turn off the display if you don&apos;t interact with them. When your alarm rings, the screen might be black, and you could easily miss the alert.<br/><br/>
            <b>With our fullscreen alarm:</b><br/>
            - The screen stays on until your alarm goes off, no matter how long you wait.<br/>
            - Whether you&apos;re taking a nap, studying, waiting for a meeting reminder, or setting a wake-up call, you&apos;ll always see the alarm and any important messages.<br/>
            - You can rest or focus without worrying about missing your alarm because the display turned off.<br/><br/>
            <b>Without this feature:</b><br/>
            - The screen will sleep, and you might not notice the alarm at all.<br/>
            - Important reminders or wake-up calls can be missed, especially if you&apos;re away from your device or not paying attention.<br/><br/>
            <b>Our fullscreen sleep prevention makes sure your alarms are always visible and reliable—so you never miss what matters most.</b>
          </p>
        </section>

        {/* FAQ Section */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Frequently Asked Questions About Online Alarm Clock
          </h2>
          <div className="space-y-4 max-w-4xl mx-auto">
            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                How many alarms can I set at once?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                You can set unlimited alarms simultaneously. Each alarm can have a custom title, time, and sound.
                The system includes smart features like duplicate time prevention, snooze functionality,
                and a queue system for handling multiple alarms that ring at the same time.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Will my alarms work if I close the browser?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                For alarms to ring, the browser tab must remain open. However, your alarm settings are automatically
                saved and will be restored when you return to the page. For best results, keep the tab open
                or consider using browser notifications if supported by your device.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Can I customize alarm sounds?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes! Choose from 22 different alarm sounds including morning clock alarms, classic and digital sounds,
                emergency alerts, casino and game sounds, gentle ringtones, and nature sounds like birds singing and rooster crowing.
                You can test each sound before setting your alarm to find the perfect wake-up tone for your needs.
                Each alarm can have its own unique sound.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                How does the snooze function work?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Our smart snooze system creates temporary alarms that don&apos;t affect your original alarm settings.
                You can choose snooze durations from 5 to 60 minutes. Snoozed alarms are temporary and won&apos;t
                create persistent recurring alarms for future days.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Does this alarm clock work on mobile devices?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes, our online alarm clock is fully optimized for mobile devices with touch-friendly controls
                and responsive design. The settings panel is specially designed for mobile screens with
                easy access to all alarm features. Works on iOS, Android, and all mobile browsers.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Can I set recurring daily alarms?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Currently, alarms are set for specific times and will ring when that time is reached.
                For daily recurring alarms, you can set multiple alarms for the same time on different days,
                or simply leave your alarms enabled - they will continue to work as long as the page remains open.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                What happens if multiple alarms ring at the same time?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Our alarm system includes a smart queue that handles multiple simultaneous alarms gracefully.
                If multiple alarms are set for the same time, they will be queued and presented one after another,
                ensuring you don&apos;t miss any important alerts.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Is this online alarm clock free to use?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes, our online alarm clock is completely free to use with no hidden costs, registration requirements,
                or premium features. All alarm functionality including multiple alarms, custom sounds, snooze,
                and settings customization are available at no charge.
              </p>
            </details>
          </div>
        </section>

        {/* Navigation Links */}
        <section className="text-center">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
            Explore More Time Management Tools
          </h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Digital Clock
            </Link>
            <Link
              href="/timer"
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Online Timer
            </Link>
            <Link
              href="/stopwatch"
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Stopwatch
            </Link>
          </div>
        </section>

        {/* Footer Content */}
        <section className="text-center text-gray-600 dark:text-gray-400 border-t pt-8 space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              Best Online Alarm Clock - Reliable Wake-Up Calls & Reminders
            </h3>
            <p className="mb-4 max-w-3xl mx-auto">
              Experience the most reliable online alarm clock with multiple alarms, custom sounds, and smart snooze functionality.
              Perfect for wake-up calls, meeting reminders, medication alerts, and any time-sensitive notifications.
              Works instantly in your browser on any device without downloads or installations.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Alarm Features</h4>
              <ul className="space-y-1">
                <li>Multiple Simultaneous Alarms</li>
                <li>Custom Alarm Titles</li>
                <li>4 Different Sounds</li>
                <li>Test Alarm Function</li>
                <li>Smart Snooze System</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Smart Features</h4>
              <ul className="space-y-1">
                <li>Duplicate Prevention</li>
                <li>Alarm Queue System</li>
                <li>Temporary Snooze Alarms</li>
                <li>Persistent Storage</li>
                <li>Enable/Disable Toggle</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Use Cases</h4>
              <ul className="space-y-1">
                <li>Wake-Up Alarms</li>
                <li>Meeting Reminders</li>
                <li>Medication Alerts</li>
                <li>Study Session Timers</li>
                <li>Daily Task Reminders</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Compatibility</h4>
              <ul className="space-y-1">
                <li>All Modern Browsers</li>
                <li>Mobile Responsive</li>
                <li>Cross-Platform</li>
                <li>No Installation Required</li>
                <li>Touch-Friendly Controls</li>
              </ul>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
}
