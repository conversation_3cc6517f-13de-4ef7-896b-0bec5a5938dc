"use client";

import { useState, useEffect } from "react";
import { Navbar } from "./navbar";
import { FullscreenExitButton } from "@/components/ui/fullscreen-exit-button";
import { isFullscreenActive, addFullscreenChangeListener } from "@/lib/fullscreen";

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const [isFullScreen, setIsFullScreen] = useState(false);

  useEffect(() => {
    const handleFullScreenChange = () => {
      const isFullScreenNow = isFullscreenActive();

      setIsFullScreen(isFullScreenNow);
    };

    // Use the utility function to add cross-browser event listeners
    const cleanup = addFullscreenChangeListener(handleFullScreenChange);

    return cleanup;
  }, []);

  return (
    <div className="min-h-screen text-foreground">
      {/* Show navbar only when not in fullscreen mode */}
      {!isFullScreen && <Navbar />}

      {/* Show fullscreen exit button when in any fullscreen mode */}
      <FullscreenExitButton isVisible={isFullScreen} />

      <main className={`${isFullScreen ? "" : "pt-16"}`}>
        {children}
      </main>
    </div>
  );
}
