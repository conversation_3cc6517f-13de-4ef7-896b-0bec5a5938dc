"use client";

import { useState, useEffect } from "react";
import { formatTime, getShortWeekday, getShortMonth } from "@/lib/utils";
import { City, getCityTime, getAllCities, getTimeOffset, formatTimeOffsetShort } from "@/lib/timezone";
import { getCountryFlag } from "@/lib/country-flags";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Plus, Search, Trash2 } from "lucide-react";

interface MultiCityClockProps {
  showSeconds?: boolean;
  use12Hours?: boolean;
  textColor?: string;
}

interface CityTimeDisplay {
  city: City;
  time: Date;
}

const STORAGE_KEY = "selectedCities";

export function MultiCityClock({
  showSeconds = true,
  use12Hours = false,
  textColor = "#ffffff",
}: MultiCityClockProps) {
  const [selectedCities, setSelectedCities] = useState<City[]>([]);
  const [cityTimes, setCityTimes] = useState<CityTimeDisplay[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoaded, setIsLoaded] = useState(false);

  const allCities = getAllCities();

  // Filter cities based on search term
  const filteredCities = allCities.filter(city => 
    city.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    city.country.toLowerCase().includes(searchTerm.toLowerCase())
  ).filter(city => !selectedCities.some(selected => selected.id === city.id));

  // Load selected cities from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const cityIds = JSON.parse(saved);
        const cities = cityIds.map((id: string) => allCities.find(city => city.id === id)).filter(Boolean);
        setSelectedCities(cities);
      }
    } catch (error) {
      console.error("Failed to load selected cities:", error);
    }
    setIsLoaded(true);
  }, [allCities]);

  // Save selected cities to localStorage
  useEffect(() => {
    if (isLoaded) {
      try {
        const cityIds = selectedCities.map(city => city.id);
        localStorage.setItem(STORAGE_KEY, JSON.stringify(cityIds));
      } catch (error) {
        console.error("Failed to save selected cities:", error);
      }
    }
  }, [selectedCities, isLoaded]);

  // Update times every second
  useEffect(() => {
    const updateTimes = () => {
      const newTimes = selectedCities.map(city => ({
        city,
        time: getCityTime(city)
      }));
      setCityTimes(newTimes);
    };

    updateTimes();
    const timer = setInterval(updateTimes, 1000);

    return () => clearInterval(timer);
  }, [selectedCities]);

  const addCity = (city: City) => {
    if (selectedCities.length < 5 && !selectedCities.some(selected => selected.id === city.id)) {
      setSelectedCities(prev => [...prev, city]);
      setSearchTerm("");
      setIsDialogOpen(false);
    }
  };

  const removeCity = (cityId: string) => {
    setSelectedCities(prev => prev.filter(city => city.id !== cityId));
  };

  const canAddMore = selectedCities.length < 5;

  if (selectedCities.length === 0 && isLoaded) {
    return (
      <div className="mt-8">
        <div className="text-center">
          <Button
            onClick={() => setIsDialogOpen(true)}
            className="bg-white/20 hover:bg-white/30 text-white border border-white/10 backdrop-blur-xl shadow-xl"
            size="lg"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add World Cities
          </Button>
        </div>

        {/* City Search Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Add City Time</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search cities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              </div>

              <div className="max-h-96 overflow-y-auto">
                <div className="grid gap-2">
                  {filteredCities.slice(0, 20).map((city) => (
                    <button
                      key={city.id}
                      onClick={() => addCity(city)}
                      className="flex items-center justify-between p-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{getCountryFlag(city.country)}</span>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {city.name}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            {city.country} • {city.timezone}
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        UTC{city.utcOffset >= 0 ? '+' : ''}{city.utcOffset}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  return (
    <>
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes marquee {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
          }
        `
      }} />
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-20 w-full px-4">
      {/* City Times Display */}
      <div className="flex flex-wrap gap-2 md:gap-3 justify-center items-stretch max-w-full">
        {cityTimes.map(({ city, time }) => {
          const timeOffset = getTimeOffset(city);
          const offsetTextShort = formatTimeOffsetShort(timeOffset);
          const dateStr = `${getShortWeekday(time)} ${getShortMonth(time)} ${time.getDate()}`;

          return (
            <div
              key={city.id}
              className="bg-white/20 shadow-xl backdrop-blur-xl rounded-lg p-2 md:p-3 border border-white/10 relative group hover:bg-white/25 transition-all duration-200
                         w-[calc(50%-4px)] sm:w-[calc(33.333%-8px)] md:w-auto md:min-w-[180px] md:max-w-[220px]
                         flex-shrink-0"
            >
              {/* Remove button - appears on hover above the card */}
              <button
                onClick={() => removeCity(city.id)}
                className="absolute -top-2 -right-2 md:-top-3 md:-right-3 bg-red-500/80 hover:bg-red-500 text-white rounded-full p-1 md:p-1.5 opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg backdrop-blur-sm"
              >
                <Trash2 className="w-2.5 h-2.5 md:w-3 md:h-3" />
              </button>

              {/* Flag - fixed at top left */}
              <div className="absolute top-2 left-2">
                <span className="text-lg md:text-xl">{getCountryFlag(city.country)}</span>
              </div>

              {/* Timezone offset - fixed at top right */}
              <div className="absolute top-2 right-2">
                <div
                  className="text-xs opacity-70 text-center whitespace-nowrap"
                  style={{
                    color: textColor,
                    fontFamily: 'monospace'
                  }}
                >
                  {offsetTextShort}
                </div>
              </div>

              <div className="flex flex-col gap-1.5 md:gap-2 items-center w-full pt-6">
                {/* City name with marquee effect */}
                <div className="w-full px-6 overflow-hidden relative">
                  <div
                    className="font-medium text-center text-xs md:text-sm whitespace-nowrap"
                    style={{
                      color: textColor,
                      fontFamily: 'monospace',
                      animation: city.name.length > 12 ? 'marquee 3s linear infinite' : 'none'
                    }}
                  >
                    {city.name}
                  </div>
                </div>

                {/* Time */}
                <div
                  className="font-mono font-bold tabular-nums text-center text-sm md:text-base"
                  style={{
                    color: textColor,
                    fontFamily: 'monospace'
                  }}
                >
                  {formatTime(time, showSeconds, use12Hours)}
                </div>

                {/* Date */}
                <div
                  className="text-xs opacity-80 text-center"
                  style={{
                    color: textColor,
                    fontFamily: 'monospace'
                  }}
                >
                  {dateStr}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Add More Cities Button */}
      {canAddMore && (
        <div className="flex justify-center mt-3">
          <Button
            onClick={() => setIsDialogOpen(true)}
            variant="outline"
            className="bg-white/20 hover:bg-white/30 text-white border-white/10 backdrop-blur-xl shadow-xl"
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add City ({selectedCities.length}/5)
          </Button>
        </div>
      )}

      {/* City Search Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Add City Time</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search cities..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              />
            </div>

            <div className="max-h-96 overflow-y-auto">
              <div className="grid gap-2">
                {filteredCities.slice(0, 20).map((city) => (
                  <button
                    key={city.id}
                    onClick={() => addCity(city)}
                    disabled={!canAddMore}
                    className="flex items-center justify-between p-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getCountryFlag(city.country)}</span>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {city.name}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          {city.country} • {city.timezone}
                        </div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      UTC{city.utcOffset >= 0 ? '+' : ''}{city.utcOffset}
                    </div>
                  </button>
                ))}
              </div>
            </div>
            
            {!canAddMore && (
              <div className="text-center text-sm text-gray-500 dark:text-gray-400 py-2">
                Maximum 5 cities can be added
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
    </>
  );
}
